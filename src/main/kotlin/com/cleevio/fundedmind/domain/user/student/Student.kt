package com.cleevio.fundedmind.domain.user.student

import com.cleevio.fundedmind.application.common.util.ifFalse
import com.cleevio.fundedmind.application.module.user.student.exception.CannotUpgradeStudentTierException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasInsufficientGameLevelException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasNoAccessToMentoringException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasNoActiveDiscordException
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasWrongStudentTierException
import com.cleevio.fundedmind.domain.SoftDeletableEntity
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.Questionnaire
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.hibernate.annotations.Type
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.Instant
import java.util.UUID

/**
 * Student is created once onboarding is finished. (see Onboarding)
 */
@Table(name = "student")
@Entity
@DynamicUpdate
class Student private constructor(
    id: UUID,
    profilePictureFileId: UUID?,
    firstName: String,
    lastName: String,
    phone: String,
    biography: String?,
    country: Country,
    studentTier: StudentTier,
    questionnaire: Questionnaire,
    discordSubscription: Boolean,
    discordSubscriptionExpiresAt: Instant?,
    firstNameVocative: String,
    lastNameVocative: String,
    tierUpgradedAt: Instant,
    networkingVisibility: NetworkingVisibility,
    levelVisibility: LevelVisibility,
    locationId: UUID?,
    gameLevel: GameLevel,
) : SoftDeletableEntity(id) {

    @File(type = FileType.STUDENT_PROFILE_PICTURE)
    var profilePictureFileId: UUID? = profilePictureFileId
        private set
    var firstName: String = firstName
        private set
    var lastName: String = lastName
        private set
    var firstNameVocative: String = firstNameVocative
        private set
    var lastNameVocative: String = lastNameVocative
        private set
    var phone: String? = phone // nullable because we have released with nullable but we no longer allow nulls
        private set
    var biography: String? = biography
        private set
    var discordSubscription: Boolean = discordSubscription
        private set
    var discordSubscriptionExpiresAt: Instant? = discordSubscriptionExpiresAt
        private set

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var questionnaire: Questionnaire = questionnaire
        private set

    @Enumerated(EnumType.STRING)
    var country: Country = country
        private set

    @Enumerated(EnumType.STRING)
    var studentTier: StudentTier = studentTier
        private set

    var tierUpgradedAt: Instant = tierUpgradedAt
        private set

    @Enumerated(EnumType.STRING)
    var networkingVisibility: NetworkingVisibility = networkingVisibility
        private set

    @Enumerated(EnumType.STRING)
    var levelVisibility: LevelVisibility = levelVisibility
        private set

    var locationId: UUID? = locationId
        private set

    @Enumerated(EnumType.STRING)
    var gameLevel: GameLevel = gameLevel
        private set

    val fullName: String
        get() = "$firstName $lastName"

    val formatedQuestionnaire: String
        get() = questionnaire.format()

    val hasMentoringAccess: Boolean
        get() = this.studentTier != StudentTier.BASECAMP

    companion object {
        fun fromOnboarding(
            id: UUID,
            profilePictureFileId: UUID?,
            studentTier: StudentTier,
            firstName: String,
            lastName: String,
            phone: String,
            biography: String?,
            country: Country,
            questionnaire: Questionnaire,
            firstNameVocative: String,
            lastNameVocative: String,
            tierUpgradedAt: Instant,
            locationId: UUID?,
        ) = Student(
            id = id,
            profilePictureFileId = profilePictureFileId,
            studentTier = studentTier,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            questionnaire = questionnaire,
            discordSubscription = false,
            discordSubscriptionExpiresAt = null,
            firstNameVocative = firstNameVocative,
            lastNameVocative = lastNameVocative,
            tierUpgradedAt = tierUpgradedAt,
            networkingVisibility = NetworkingVisibility.ENABLED,
            levelVisibility = LevelVisibility.ENABLED,
            locationId = locationId,
            gameLevel = GameLevel.ONE,
        )
    }

    fun changeProfilePicture(fileId: UUID?) {
        this.profilePictureFileId = fileId
    }

    fun updateTier(studentTier: StudentTier) {
        this.studentTier = studentTier
    }

    fun updateProfile(
        firstName: String,
        lastName: String,
        phone: String,
        biography: String?,
        country: Country,
        firstNameVocative: String,
        lastNameVocative: String,
        locationId: UUID?,
    ) {
        this.firstName = firstName
        this.lastName = lastName
        this.phone = phone
        this.biography = biography
        this.country = country
        this.firstNameVocative = firstNameVocative
        this.lastNameVocative = lastNameVocative
        this.locationId = locationId
    }

    fun activateDiscordSubscription(expiresAt: Instant) {
        this.discordSubscription = true
        this.discordSubscriptionExpiresAt = expiresAt
    }

    fun deactivateDiscordSubscription() {
        this.discordSubscription = false
    }

    fun upgradeToMasterclass(tierUpgradedAt: Instant = Instant.now()) {
        upgradeTier(StudentTier.MASTERCLASS, tierUpgradedAt)
    }

    fun upgradeToExclusive(tierUpgradedAt: Instant = Instant.now()) {
        upgradeTier(StudentTier.EXCLUSIVE, tierUpgradedAt)
    }

    fun updateQuestionnaire(questionnaire: Questionnaire) {
        this.questionnaire = questionnaire
    }

    fun patchPrivacy(
        networkingVisibility: NetworkingVisibility?,
        levelVisibility: LevelVisibility?,
    ) {
        networkingVisibility?.let { this.networkingVisibility = it }
        levelVisibility?.let { this.levelVisibility = it }
    }

    fun patchLocation(locationId: UUID?) {
        this.locationId = locationId
    }

    fun updateGameLevel(gameLevel: GameLevel) {
        this.gameLevel = gameLevel
    }

    private fun upgradeTier(
        desiredTier: StudentTier,
        tierUpgradedAt: Instant,
    ) {
        checkCurrentTierIsBellow(desiredTier)
        this.studentTier = desiredTier
        this.tierUpgradedAt = tierUpgradedAt
    }

    fun checkCurrentTierIsBellow(other: StudentTier) {
        this.studentTier.isBelow(other).ifFalse {
            throw CannotUpgradeStudentTierException(
                "Student: '$id' has tier: $studentTier that is not below '$other'",
            )
        }
    }

    fun checkStudentTierIn(vararg allowedTiers: StudentTier) {
        if (this.studentTier !in allowedTiers) {
            throw StudentHasWrongStudentTierException(
                "Student: '$id' has wrong tier: $studentTier, allowed: ${allowedTiers.asList()}",
            )
        }
    }

    fun checkGameLevelIsAtLeast(minimumLevel: GameLevel) {
        if (this.gameLevel.order < minimumLevel.order) {
            throw StudentHasInsufficientGameLevelException(
                "Student: '$id' has wrong game level: $gameLevel, allowed: $minimumLevel and above",
            )
        }
    }

    fun checkHasActiveDiscord() {
        if (!this.discordSubscription) {
            throw StudentHasNoActiveDiscordException("Student: '$id' has no active discord subscription")
        }
    }

    fun isLockedForStudent(
        allowedTiers: List<StudentTier>,
        allowedDiscordUser: Boolean,
    ): Boolean {
        // If no one is allowed (empty tiers and no Discord users), it's locked
        if (allowedTiers.isEmpty() && !allowedDiscordUser) {
            return true
        }

        val accessViaTier = this.studentTier in allowedTiers
        val accessViaDiscord = allowedDiscordUser && this.discordSubscription

        val hasAccess = accessViaTier || accessViaDiscord

        val isLocked = !hasAccess

        return isLocked
    }

    fun checkHasMentoringAccess() {
        if (!this.hasMentoringAccess) {
            throw StudentHasNoAccessToMentoringException("Student: '$id' has no access to mentoring")
        }
    }
}

@Repository
interface StudentRepository : JpaRepository<Student, UUID>
