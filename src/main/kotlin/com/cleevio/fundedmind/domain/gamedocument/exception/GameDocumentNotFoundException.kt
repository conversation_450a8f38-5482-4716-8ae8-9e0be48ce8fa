package com.cleevio.fundedmind.domain.gamedocument.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class GameDocumentNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_DOCUMENT_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class GameDocumentTruthScoreCannotBeNegativeException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_DOCUMENT_TRUTH_SCORE_CANNOT_BE_NEGATIVE,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class GameDocumentPayoutAmountCannotBeNegativeException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_DOCUMENT_PAYOUT_AMOUNT_CANNOT_BE_NEGATIVE,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class GameDocumentPayoutAmountIsRequiredException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_DOCUMENT_PAYOUT_AMOUNT_IS_REQUIRED,
    message = message,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class GameDocumentNotRelatedToStudentException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_DOCUMENT_NOT_RELATED_TO_STUDENT,
    message = message,
)
