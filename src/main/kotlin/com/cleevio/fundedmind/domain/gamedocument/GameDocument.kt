package com.cleevio.fundedmind.domain.gamedocument

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.UpdatableEntity
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.file.constant.File
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentNotRelatedToStudentException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentTruthScoreCannotBeNegativeException
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

@Table(name = "game_document")
@Entity
@DynamicUpdate
class GameDocument private constructor(
    id: UUID,
    studentId: UUID,
    type: GameDocumentType,
    issuingCompany: IssuingCompany,
    payoutAmount: BigDecimal?,
    reachedLevel: GameLevel,
    payoutDate: LocalDate,
    gameDocumentFileId: UUID?,
    truthScore: Int,
    state: GameDocumentApprovalState,
    denyMessage: String?,
    scoreMessage: String?,
    approvedAt: Instant?,
    deniedAt: Instant?,
) : UpdatableEntity(id) {

    init {
        checkTruthScoreIsPositiveOrZero(truthScore)
        checkTypeWithPayoutAmount(type, payoutAmount)
    }

    var studentId: UUID = studentId
        private set

    @Enumerated(EnumType.STRING)
    var type: GameDocumentType = type
        private set

    @Enumerated(EnumType.STRING)
    var issuingCompany: IssuingCompany = issuingCompany
        private set

    var payoutAmount: BigDecimal? = payoutAmount
        private set

    @Enumerated(EnumType.STRING)
    var reachedLevel: GameLevel = reachedLevel
        private set

    var payoutDate: LocalDate = payoutDate
        private set

    @File(type = FileType.GAME_DOCUMENT)
    var gameDocumentFileId: UUID? = gameDocumentFileId
        private set

    var truthScore: Int = truthScore
        private set

    @Enumerated(EnumType.STRING)
    var state: GameDocumentApprovalState = state
        private set

    var denyMessage: String? = denyMessage
        private set

    var scoreMessage: String? = scoreMessage
        private set

    var approvedAt: Instant? = approvedAt
        private set

    var deniedAt: Instant? = deniedAt
        private set

    companion object {
        fun newGameDocument(
            id: UUID = UUIDv7.randomUUID(),
            studentId: UUID,
            type: GameDocumentType,
            issuingCompany: IssuingCompany,
            payoutAmount: BigDecimal?,
            reachedLevel: GameLevel,
            payoutDate: LocalDate,
            truthScore: Int,
            scoreMessage: String?,
        ) = GameDocument(
            id = id,
            studentId = studentId,
            type = type,
            issuingCompany = issuingCompany,
            payoutAmount = payoutAmount,
            reachedLevel = reachedLevel,
            payoutDate = payoutDate,
            gameDocumentFileId = null,
            truthScore = truthScore,
            state = GameDocumentApprovalState.WAITING,
            denyMessage = null,
            scoreMessage = scoreMessage,
            approvedAt = null,
            deniedAt = null,
        )
    }

    fun changeGameDocumentFile(fileId: UUID?) {
        this.gameDocumentFileId = fileId
    }

    fun approve(now: Instant = Instant.now()) {
        this.state = GameDocumentApprovalState.APPROVED
        this.approvedAt = now
    }

    fun deny(
        message: String,
        now: Instant = Instant.now(),
    ) {
        this.state = GameDocumentApprovalState.DENIED
        this.denyMessage = message
        this.deniedAt = now
    }

    fun update(
        type: GameDocumentType,
        issuingCompany: IssuingCompany,
        payoutAmount: BigDecimal?,
        reachedLevel: GameLevel,
        payoutDate: LocalDate,
        truthScore: Int,
        scoreMessage: String?,
    ) {
        checkTruthScoreIsPositiveOrZero(truthScore)
        checkTypeWithPayoutAmount(type, payoutAmount)

        this.type = type
        this.issuingCompany = issuingCompany
        this.payoutAmount = payoutAmount
        this.reachedLevel = reachedLevel
        this.payoutDate = payoutDate
        this.truthScore = truthScore
        this.scoreMessage = scoreMessage
    }

    fun checkRelatedToStudent(studentId: UUID) {
        if (this.studentId != studentId) {
            throw GameDocumentNotRelatedToStudentException(
                "GameDocument: '$id' is not related to student: '$studentId'",
            )
        }
    }

    private fun checkTruthScoreIsPositiveOrZero(truthScore: Int) {
        if (truthScore >= 0) return

        throw GameDocumentTruthScoreCannotBeNegativeException(
            "GameDocument: '$id' has negative truth score: $truthScore",
        )
    }

    private fun checkTypeWithPayoutAmount(
        type: GameDocumentType,
        payoutAmount: BigDecimal?,
    ) {
        when (type) {
            GameDocumentType.PAYOUT -> checkPayoutAmountIsPresentAndNotNegative(payoutAmount)
            GameDocumentType.CERTIFICATE -> return
            GameDocumentType.BACKTESTING -> return
        }
    }

    private fun checkPayoutAmountIsPresentAndNotNegative(payoutAmount: BigDecimal?) {
        if (payoutAmount == null) {
            throw GameDocumentPayoutAmountIsRequiredException(
                "GameDocument: '$id' must have payout amount not null.",
            )
        }
        if (payoutAmount < BigDecimal.ZERO) {
            throw GameDocumentPayoutAmountCannotBeNegativeException(
                "GameDocument: '$id' has negative payout amount: $payoutAmount",
            )
        }
    }
}

@Repository
interface GameDocumentRepository : JpaRepository<GameDocument, UUID> {
    fun findAllByStudentId(studentId: UUID): List<GameDocument>

    @Query(
        """
        SELECT COALESCE(SUM(gd.payoutAmount), 0)
        FROM GameDocument gd
        WHERE gd.type = :type
        AND gd.payoutAmount IS NOT NULL
        AND gd.state = :state
        AND gd.studentId = :studentId
        """,
    )
    fun sumPayoutAmountByTypeAndState(
        studentId: UUID,
        type: GameDocumentType,
        state: GameDocumentApprovalState,
    ): BigDecimal
}
