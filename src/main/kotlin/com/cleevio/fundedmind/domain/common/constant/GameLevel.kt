package com.cleevio.fundedmind.domain.common.constant

import com.cleevio.fundedmind.application.common.util.isNegative
import java.math.BigDecimal

enum class GameLevel(val order: Int, val levelPayoutGoal: Int?) {
    ZERO(order = 0, levelPayoutGoal = null),
    ONE(order = 1, levelPayoutGoal = null),
    TWO(order = 2, levelPayoutGoal = null),
    THREE(order = 3, levelPayoutGoal = null),
    FOUR(order = 4, levelPayoutGoal = 0), // first payout can be of any value therefore zero amount
    FIVE(order = 5, levelPayoutGoal = 5_000),
    SIX(order = 6, levelPayoutGoal = 10_000),
    SEVEN(order = 7, levelPayoutGoal = 20_000),
    EIGHT(order = 8, levelPayoutGoal = 50_000),
    NINE(order = 9, levelPayoutGoal = 100_000),
    TEN(order = 10, levelPayoutGoal = null),
    ;

    companion object {
        /**
         * Determines what level would be reached with a given payout amount.
         *
         *
         * @param payoutAmount The total payout amount
         * @return The level that would be reached with the given amount
         */
        fun determineLevelByPayoutAmount(payoutAmount: BigDecimal): GameLevel {
            check(!payoutAmount.isNegative()) { "Payout amount cannot be negative: $payoutAmount" }

            val levelsWithGoals = GameLevel
                .entries
                .filter { it.levelPayoutGoal != null }
                .sortedBy { it.order }

            var cumulativeAmount = BigDecimal.ZERO

            for (level in levelsWithGoals) {
                val goalAmount = level.levelPayoutGoal!!.toBigDecimal()

                if (cumulativeAmount + goalAmount >= payoutAmount) {
                    return level
                }

                cumulativeAmount += goalAmount
            }

            // If the amount exceeds all level goals, return the highest level with goals
            return levelsWithGoals.last()
        }
    }
}
