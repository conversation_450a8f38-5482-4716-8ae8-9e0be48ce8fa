package com.cleevio.fundedmind.application.module.user.student.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class StudentNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class StudentAlreadyOnboardedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_ALREADY_ONBOARDED,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class CannotUpgradeStudentTierException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.CANNOT_UPGRADE_STUDENT_TIER,
    message = message,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class StudentHasNoAccessToMentoringException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_HAS_NO_ACCESS_TO_MENTORING,
    message = message,
)

@ResponseStatus(HttpStatus.FORBIDDEN)
class StudentHasNoActiveDiscordException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_HAS_NO_ACTIVE_DISCORD,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class StudentHasWrongStudentTierException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_HAS_WRONG_STUDENT_TIER,
    message = message,
)

@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
class StudentHasInsufficientGameLevelException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_HAS_INSUFFICIENT_GAME_LEVEL,
    message = message,
)

@ResponseStatus(HttpStatus.BAD_REQUEST)
class CannotBuyDiscordSubscriptionException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.CANNOT_BUY_DISCORD_SUBSCRIPTION,
    message = message,
)

@ResponseStatus(HttpStatus.NOT_FOUND)
class StudentDiscordNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_DISCORD_NOT_FOUND,
    message = message,
)

@ResponseStatus(HttpStatus.CONFLICT)
class StudentDiscordAlreadyConnectedException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.STUDENT_DISCORD_ALREADY_CONNECTED,
    message = message,
)
