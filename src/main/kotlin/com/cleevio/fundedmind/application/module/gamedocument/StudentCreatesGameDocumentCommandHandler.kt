package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.constants.Locks
import com.cleevio.fundedmind.application.common.util.isNegative
import com.cleevio.fundedmind.application.module.gamedocument.command.StudentCreatesGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentCreatedEvent
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.gamedocument.CreateGameDocumentService
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Component
class StudentCreatesGameDocumentCommandHandler(
    private val createGameDocumentService: CreateGameDocumentService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val studentFinderService: StudentFinderService,
    private val gameDocumentFinderService: GameDocumentFinderService,
) : CommandHandler<IdResult, StudentCreatesGameDocumentCommand> {

    override val command = StudentCreatesGameDocumentCommand::class

    @Transactional
    @Lock(module = Locks.GameDocument.MODULE, lockName = Locks.GameDocument.CREATE)
    override fun handle(@LockFieldParameter("studentId") command: StudentCreatesGameDocumentCommand): IdResult {
        val student = studentFinderService
            .getById(command.studentId)
            .apply {
                checkGameLevelIsAtLeast(GameLevel.ONE)
            }

        val currentLevel = student.gameLevel

        val computedReachedLevel = when (command.type) {
            GameDocumentType.BACKTESTING -> {
                when (currentLevel) {
                    GameLevel.ONE -> GameLevel.TWO
                    else -> currentLevel
                }
            }

            GameDocumentType.CERTIFICATE -> {
                when (currentLevel) {
                    GameLevel.ONE, GameLevel.TWO -> GameLevel.THREE
                    else -> currentLevel
                }
            }

            GameDocumentType.PAYOUT -> {
                checkPayoutAmountIsPresentAndNotNegative(command.payoutAmount)

                val currentStudentTotalPayout = gameDocumentFinderService.sumStudentApprovedPayouts(command.studentId)

                GameLevel.determineLevelByPayoutAmount(
                    payoutAmount = currentStudentTotalPayout + command.payoutAmount!!,
                )
            }
        }

        val gameDocument = createGameDocumentService.create(
            studentId = command.studentId,
            type = command.type,
            issuingCompany = command.issuingCompany,
            amount = command.payoutAmount,
            reachedLevel = computedReachedLevel,
            payoutDate = command.payoutDate,
            truthScore = command.truthScore,
            scoreMessage = command.scoreMessage,
        )

        applicationEventPublisher.publishEvent(GameDocumentCreatedEvent(gameDocumentId = gameDocument.id))

        return IdResult(id = gameDocument.id)
    }

    private fun checkPayoutAmountIsPresentAndNotNegative(payoutAmount: BigDecimal?) {
        if (payoutAmount == null) {
            throw GameDocumentPayoutAmountIsRequiredException("Payout amount is required")
        }
        if (payoutAmount.isNegative()) {
            throw GameDocumentPayoutAmountCannotBeNegativeException("PayoutAmount cannot be negative")
        }
    }
}
