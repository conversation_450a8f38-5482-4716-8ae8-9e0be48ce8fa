package com.cleevio.fundedmind.application.module.gamedocument.query

import com.cleevio.fundedmind.adapter.`in`.InfiniteScroll
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.application.common.command.DocumentResult
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.common.query.Query
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.Valid
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.Year
import java.util.UUID

data class GetGameDocumentDetailQuery(
    val gameDocumentId: UUID,
) : Query<GetGameDocumentDetailQuery.Result> {

    @Schema(name = "GetGameDocumentDetailResult")
    data class Result(
        val gameDocumentId: UUID,
        val studentId: UUID,
        val type: GameDocumentType,
        val issuingCompany: IssuingCompany,
        val payoutAmount: BigDecimal?,
        val reachedLevel: GameLevel,
        val payoutDate: LocalDate,
        val gameDocumentFile: DocumentResult?,
        val truthScore: Int,
        val state: GameDocumentApprovalState,
        val denyMessage: String?,
        val scoreMessage: String?,
    )
}

data class SearchGameDocumentsQuery(
    @field:Valid val infiniteScroll: InfiniteScroll<UUID>,
    @field:Valid val filter: Filter,
) : Query<InfiniteScrollSlice<SearchGameDocumentsQuery.Result, UUID>> {

    data class Filter(
        val studentId: UUID?,
        val state: GameDocumentApprovalState?,
    )

    @Schema(name = "SearchGameDocumentsResult")
    data class Result(
        val gameDocumentId: UUID,
        val student: SearchGameDocumentStudent,
        val type: GameDocumentType,
        val issuingCompany: IssuingCompany,
        val payoutAmount: BigDecimal?,
        val reachedLevel: GameLevel,
        val payoutDate: LocalDate,
        val gameDocumentFile: DocumentResult?,
        val truthScore: Int,
        val state: GameDocumentApprovalState,
        val denyMessage: String?,
    )

    @Schema(name = "SearchGameDocumentStudent")
    data class SearchGameDocumentStudent(
        val studentId: UUID,
        val firstName: String,
        val lastName: String,
        val email: String,
        val profilePicture: ImageResult?,
        val currentGameLevel: GameLevel,
    )
}

data class AdminGetsPayoutOverviewQuery(
    val year: Year = Year.now(),
) : Query<AdminGetsPayoutOverviewQuery.Result> {

    @Schema(name = "AdminGetsPayoutOverviewResult")
    data class Result(
        val offsetTotalPayout: BigDecimal,
        val realTotalPayout: BigDecimal,
        val approvedPayouts: Int,
    )
}

data class GamePayoutOverviewQuery(
    val year: Year = Year.now(),
) : Query<GamePayoutOverviewQuery.Result> {

    @Schema(name = "GamePayoutOverviewResult")
    data class Result(
        val currentTotalPayout: BigDecimal,
        val currentPayoutGoal: BigDecimal,
        val year: Int,
        val latestStudentPayouts: List<LatestStudentPayout>,
    )

    @Schema(name = "LatestStudentPayout")
    data class LatestStudentPayout(
        val firstName: String,
        val profilePicture: ImageResult?,
        val approvedAt: Instant,
        val payoutAmount: BigDecimal,
    )
}

data class StudentGetsGameProgressOverviewQuery(
    val studentId: UUID,
) : Query<StudentGetsGameProgressOverviewQuery.Result> {

    @Schema(name = "StudentGetsGameProgressOverviewResult")
    data class Result(
        val firstName: String,
        val lastName: String,
        val studentTier: StudentTier,
        val gameLevel: GameLevel,
        val city: String?,
        val overallPayoutAmount: BigDecimal,

        val levelTwoProgress: LevelTwoProgress?,
        val levelThreeProgress: LevelThreeProgress?,
        val levelFourProgress: LevelFourProgress?,
        val levelFiveProgress: LevelWithPayoutProgress?,
        val levelSixProgress: LevelWithPayoutProgress?,
        val levelSevenProgress: LevelWithPayoutProgress?,
        val levelEightProgress: LevelWithPayoutProgress?,
        val levelNineProgress: LevelWithPayoutProgress?,
    )

    @Schema(name = "GameProgressLevelTwoProgress")
    data class LevelTwoProgress(
        val backtestingState: GameDocumentApprovalState?,
        val strategyModuleFinished: Boolean,
    )

    @Schema(name = "GameProgressLevelThreeProgress")
    data class LevelThreeProgress(
        val certificateState: GameDocumentApprovalState,
    )

    @Schema(name = "GameProgressLevelFourProgress")
    data class LevelFourProgress(
        val firstPayoutState: GameDocumentApprovalState,
    )

    @Schema(name = "GameProgressLevelFiveProgress")
    data class LevelWithPayoutProgress(
        @Schema(description = "Compute progress bar e.g. 3200 / 5000 (payoutProgress / payoutGoal)")
        val payoutProgressInLevel: BigDecimal,
        val payoutGoal: BigDecimal,
    )
}
