package com.cleevio.fundedmind.application.module.notification.event.listener

import com.cleevio.fundedmind.application.module.gamedocument.event.GameDocumentCreatedEvent
import com.cleevio.fundedmind.application.module.gamedocument.finder.GameDocumentFinderService
import com.cleevio.fundedmind.application.module.notification.port.out.SendNotificationMessagePort
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import io.sentry.spring.jakarta.tracing.SentryTransaction
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class GameDocumentNotificationEventListener(
    private val sendNotificationMessagePort: SendNotificationMessagePort,
    private val gameDocumentFinderService: GameDocumentFinderService,
    private val studentFinderService: StudentFinderService,
) {

    @SentryTransaction(operation = "async.notification.game-document-created")
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handleGameDocumentCreatedEvent(event: GameDocumentCreatedEvent) {
        val gameDocument = gameDocumentFinderService.getById(event.gameDocumentId)
        val student = studentFinderService.getById(gameDocument.studentId)

        sendNotificationMessagePort.newGameDocument(
            studentName = student.fullName,
            gameDocumentId = gameDocument.id,
            gameDocumentType = gameDocument.type,
            payoutAmount = gameDocument.payoutAmount,
            payoutDate = gameDocument.payoutDate,
            truthScore = gameDocument.truthScore,
            scoreMessage = gameDocument.scoreMessage,
        )
    }
}
