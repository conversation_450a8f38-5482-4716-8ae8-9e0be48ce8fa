package com.cleevio.fundedmind.application.module.notification.port.out

import java.util.UUID

interface SendNotificationMessagePort {
    fun newCommentUnderLesson(
        commentId: UUID,
        commentText: String,
        commentOwnerName: String,
        threadId: UUID?,
        lessonId: UUID,
        lessonName: String,
        courseModuleId: UUID,
        courseModuleName: String,
        courseId: UUID,
        courseName: String,
    )
}
