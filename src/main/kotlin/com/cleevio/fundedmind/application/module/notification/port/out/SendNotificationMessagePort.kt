package com.cleevio.fundedmind.application.module.notification.port.out

import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

interface SendNotificationMessagePort {
    fun newCommentUnderLesson(
        commentId: UUID,
        commentText: String,
        commentOwnerName: String,
        threadId: UUID?,
        lessonId: UUID,
        lessonName: String,
        courseModuleId: UUID,
        courseModuleName: String,
        courseId: UUID,
        courseName: String,
    )

    fun newGameDocument(
        studentName: String,
        gameDocumentId: UUID,
        gameDocumentType: GameDocumentType,
        payoutAmount: BigDecimal?,
        payoutDate: LocalDate,
        truthScore: Int,
        scoreMessage: String?,
    )
}
