package com.cleevio.fundedmind.application.common.command

import com.cleevio.fundedmind.application.common.validation.NotBlankAndLimited
import com.cleevio.fundedmind.application.common.validation.ValidGameDocument
import com.cleevio.fundedmind.application.common.validation.ValidImage
import org.springframework.web.multipart.MultipartFile

data class ImageInput(
    @field:ValidImage val originalFile: MultipartFile,
    @field:ValidImage val compressedFile: MultipartFile,
    @field:NotBlankAndLimited val blurHash: String,
)

data class GameDocumentInput(
    @field:ValidGameDocument val originalFile: MultipartFile,
)

data class DocumentInput(
    // document is uploaded by admin and needs no validation because file types are not restricted
    val originalFile: MultipartFile,
)
