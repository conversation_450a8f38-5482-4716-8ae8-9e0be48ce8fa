package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.StudentCreatesGameDocumentRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Game Document [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/game-documents/me")
class GameDocumentMeController(
    private val commandBus: CommandBus,
) {

    @Operation(
        description = """
            Student creates new game document.
            400 - type is payout but payout amount is null
            400 - payout amount is negative
            400 - truth score is negative
        """,
    )
    @RolesAllowed(UserRole.STUDENT_ROLE)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun studentCreatesGameDocument(
        @AuthenticationPrincipal studentId: UUID,
        @RequestBody request: StudentCreatesGameDocumentRequest,
    ): IdResult = commandBus(request.toCommand(studentId))
}
