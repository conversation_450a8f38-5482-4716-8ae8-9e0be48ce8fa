package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.DocumentResult
import com.cleevio.fundedmind.application.common.command.GameDocumentInput
import com.cleevio.fundedmind.application.common.command.ImageInput
import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.file.command.StudentUploadsGameDocumentCommand
import com.cleevio.fundedmind.application.module.file.command.UserDeletesTheirProfilePictureCommand
import com.cleevio.fundedmind.application.module.file.command.UserUploadsTheirProfilePictureCommand
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@Tag(name = "File [My Profile]")
@RestController
@SwaggerBearerToken
@RequestMapping("/files/me")
class FileMeController(
    private val commandBus: CommandBus,
) {

    @Operation(
        description = """
            User uploads their profile picture replacing previous one.
        """,
    )
    @PostMapping(
        "/profile-picture",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [ApiVersion.VERSION_1_JSON],
    )
    @ResponseStatus(HttpStatus.CREATED)
    fun userUploadsTheirProfilePicture(
        @RequestPart originalFile: MultipartFile,
        @RequestPart compressedFile: MultipartFile,
        @RequestPart blurHash: String,
        @Parameter(
            description = "Type of image to upload. Allowed types are: " +
                "ONBOARDING_PROFILE_PICTURE," +
                "STUDENT_PROFILE_PICTURE," +
                "TRADER_PROFILE_PICTURE",
            required = true,
        )
        @RequestParam type: FileType,
        @AuthenticationPrincipal userId: UUID,
    ): ImageResult = commandBus(
        UserUploadsTheirProfilePictureCommand(
            image = ImageInput(originalFile = originalFile, compressedFile = compressedFile, blurHash = blurHash),
            type = type,
            userId = userId,
        ),
    )

    @Operation(
        description = """
            User deletes their profile picture.
        """,
    )
    @DeleteMapping("/profile-picture", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun userDeletesTheirProfilePicture(
        @Parameter(
            description = "Type of image to upload. Allowed types are: " +
                "ONBOARDING_PROFILE_PICTURE," +
                "STUDENT_PROFILE_PICTURE," +
                "TRADER_PROFILE_PICTURE",
            required = true,
        )
        @RequestParam type: FileType,
        @AuthenticationPrincipal userId: UUID,
    ): Unit = commandBus(
        UserDeletesTheirProfilePictureCommand(
            type = type,
            userId = userId,
        ),
    )

    @Operation(
        description = """
            Student uploads game document.
            400 - file is not of type 'GAME_DOCUMENT'
            403 - referenced GameDocument is not related to authenticated student
        """,
    )
    @RolesAllowed(UserRole.STUDENT_ROLE)
    @PostMapping(
        "/game-document",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [ApiVersion.VERSION_1_JSON],
    )
    @ResponseStatus(HttpStatus.CREATED)
    fun studentUploadsGameDocument(
        @RequestPart originalFile: MultipartFile,
        @RequestParam gameDocumentId: UUID,
        @AuthenticationPrincipal studentId: UUID,
    ): DocumentResult = commandBus(
        StudentUploadsGameDocumentCommand(
            studentId = studentId,
            gameDocument = GameDocumentInput(originalFile = originalFile),
            gameDocumentId = gameDocumentId,
        ),
    )
}
