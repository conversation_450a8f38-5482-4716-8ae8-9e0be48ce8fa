package com.cleevio.fundedmind.adapter.out.zapier

import com.cleevio.fundedmind.adapter.out.zapier.request.ZapierNewCommentUnderLessonRequest
import com.cleevio.fundedmind.application.common.util.composeLessonUrl
import com.cleevio.fundedmind.application.module.notification.port.out.SendNotificationMessagePort
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.infrastructure.config.logger
import com.cleevio.fundedmind.infrastructure.properties.FundedMindUrlPrefixProperties
import com.cleevio.fundedmind.infrastructure.properties.ZapierProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@Service
@ConditionalOnProperty(name = ["integration.zapier.enabled"], havingValue = "true")
class ZapierService(
    private val zapierConnector: ZapierConnector,
    private val fundedMindUrlPrefixProperties: FundedMindUrlPrefixProperties,
    private val zapierProperties: ZapierProperties,
) : SendNotificationMessagePort {

    override fun newCommentUnderLesson(
        commentId: UUID,
        commentText: String,
        commentOwnerName: String,
        threadId: UUID?,
        lessonId: UUID,
        lessonName: String,
        courseModuleId: UUID,
        courseModuleName: String,
        courseId: UUID,
        courseName: String,
    ) {
        zapierConnector.callWebhookSendCommentToSlack(
            ZapierNewCommentUnderLessonRequest(
                commentText = commentText,
                studentFullName = commentOwnerName,
                lessonName = lessonName,
                courseModuleName = courseModuleName,
                courseName = courseName,
                lessonUrl = composeLessonUrl(
                    urlPrefix = fundedMindUrlPrefixProperties.lesson,
                    courseId = courseId,
                    courseModuleId = courseModuleId,
                    lessonId = lessonId,
                    threadId = threadId ?: commentId,
                ),
                apiKey = zapierProperties.apiKey,
            ),
        )
    }

    override fun newGameDocument(
        studentName: String,
        gameDocumentId: UUID,
        gameDocumentType: GameDocumentType,
        payoutAmount: BigDecimal?,
        payoutDate: LocalDate,
        truthScore: Int,
        scoreMessage: String?,
    ) {
        zapierConnector.callWebhookNewGameDocument(
            ZapierNewGameDocumentRequest(
                gameDocumentId = gameDocumentId.toString(),
                studentName = studentName,
                gameDocumentType = gameDocumentType.name,
                payoutAmount = payoutAmount,
                payoutDate = payoutDate,
                truthScore = truthScore,
                scoreMessage = scoreMessage,
                apiKey = zapierProperties.apiKey,
            ),
        )
    }
}

@Service
@ConditionalOnProperty(name = ["integration.zapier.enabled"], havingValue = "false")
class DummyZapierService : SendNotificationMessagePort {
    private val logger = logger()

    override fun newCommentUnderLesson(
        commentId: UUID,
        commentText: String,
        commentOwnerName: String,
        threadId: UUID?,
        lessonId: UUID,
        lessonName: String,
        courseModuleId: UUID,
        courseModuleName: String,
        courseId: UUID,
        courseName: String,
    ) {
        logger.trace("Sending notifications via Zapier is disabled.")
    }

    override fun newGameDocument(
        studentName: String,
        gameDocumentId: UUID,
        gameDocumentType: GameDocumentType,
        payoutAmount: BigDecimal?,
        payoutDate: LocalDate,
        truthScore: Int,
        scoreMessage: String?,
    ) {
        logger.trace("Sending notifications via Zapier is disabled.")
    }
}
