# ----- <PERSON>N<PERSON> PROPERTIES -----
TIME-ZONE: UTC
REQUEST-SIZE: 100MB # same as in nginx
FILE-SIZE: 30MB

server:
  error:
    include-message: always # turned off in prod
    include-exception: true # turned off in prod
  forward-headers-strategy: NATIVE
  tomcat:
    max-swallow-size: ${REQUEST-SIZE}
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
  shutdown: graceful

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE}

  application:
    name: funded-mind-api

  threads:
    virtual:
      enabled: false

  datasource:
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    url: ${DB_URL}?reWriteBatchedInserts=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      leak-detection-threshold: 5000 # 5 sec
      connection-timeout: 15000 # 15 sec
      max-lifetime: 600000 # 10 min

  jooq:
    sql-dialect: POSTGRES

  #  https://vladmihalcea.com/spring-boot-application-properties/
  jpa:
    hibernate:
      naming:
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
        physical-strategy: com.cleevio.fundedmind.application.jpa.CustomCamelCaseToUnderscoresNamingStrategy
      ddl-auto: validate
    open-in-view: false
    show-sql: false # don't use this property, use 'net.ttddyy.dsproxy' - https://vladmihalcea.com/log-sql-spring-boot
    properties:
      dialect: org.hibernate.dialect.PostgreSQLDialect
      hibernate:
        jdbc:
          time_zone: ${TIME-ZONE}
          batch_size: 20
          fetch_size: 50
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
#        connection.provider_disables_autocommit: true
        query:
          in_clause_parameter_padding: true
          fail_on_pagination_over_collection_fetch: true
          plan_cache_max_size: 4096

  flyway:
    baselineOnMigrate: true
    enabled: true
    locations: "classpath:db/migration"
    table: _flyway_schema_history
    schemas: public

  jackson:
    default-property-inclusion: always
    time-zone: ${TIME-ZONE}
    serialization:
      indent-output: true
      order-map-entries-by-keys: true # turned off in prod
    mapper:
      sort-properties-alphabetically: true # turned off in prod

  task:
    scheduling:
      thread-name-prefix: scheduling-
      simple:
        concurrency-limit: -1
      pool:
        size: 4
    execution:
      shutdown:
        await-termination: true
        await-termination-period: PT30S
      pool:
        core-size: 16

  output:
    ansi:
      enabled: always

  servlet:
    multipart:
      max-file-size: ${FILE-SIZE}
      max-request-size: ${REQUEST-SIZE}

  mail:
    test-connection: false
    default-encoding: UTF-8
    host: ${SPRING_MAIL_HOST}
    port: ${SPRING_MAIL_PORT}
    username: ${SPRING_MAIL_USERNAME}
    password: ${SPRING_MAIL_PASSWORD}
    protocol: smtp
    properties:
      # Spring recommends explicitly setting the timeout values
      # https://docs.spring.io/spring-boot/reference/io/email.html
      # Documentation for mail properties
      # https://javaee.github.io/javamail/docs/api/index.html?com/sun/mail/smtp/package-summary.html
      mail:
        smtp:
          connectiontimeout: 5000 # in milliseconds.
          timeout: 3000 # in milliseconds.
          writetimeout: 5000 # in milliseconds.
          from: ${SPRING_MAIL_FROM}
          auth: true
          starttls:
            enable: true

springdoc:
  api-docs:
    path: /api-docs
    resolve-schema-properties: true
  swagger-ui:
    path: /api-docs/swagger-ui.html
    tagsSorter: alpha
    operationsSorter: alpha
    doc-expansion: none
    default-model-rendering: model
    filter: true
    display-request-duration: true
    show-common-extensions: true
  pre-loading-enabled: true
  show-actuator: false # Enabling actuator throws BeanInstantiationException: Failed to instantiate SwaggerConfig$$SpringCGLIB$$0]: No default constructor found
  nullable-request-parameter-enabled: true
  trim-kotlin-indent: false

sentry:
  dsn: ${SENTRY_DSN}
  exception-resolver-order: -2147483647
  send-default-pii: true
  max-request-body-size: always
  traces-sample-rate: 0.2
  attach-server-name: false
  keep-transactions-open-for-async-responses: true

firebase:
  credentials: ${FIREBASE_CREDENTIALS}

logging:
  config: classpath:logback-spring.xml
  register-shutdown-hook: true
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-50.50logger{49}){cyan} %clr(:){faint} %m%n%wEx"
  level:
    root: INFO
    #    org.springframework.security: DEBUG
    #    org.hibernate: INFO
    #    org.hibernate.SQL: DEBUG
    #    org.hibernate.orm.jdbc.bind: DEBUG # trace for values
    #    org.hibernate.stat: DEBUG
    #    org.hibernate.SQL_SLOW: INFO
    #    org.hibernate.cache: DEBUG
    #    org.jooq.tools.LoggerListener: DEBUG
    org.jooq: INFO
    org.flywaydb: INFO
    #    net.ttddyy.dsproxy.listener: debug # SQL debug
    p6spy: warn # SQL debug

# SpringBoot Actuator
management:
  server:
    port: 8282
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  health:
    mail:
      enabled: false

cleevio:
  distributed-locks:
    storage-type: POSTGRES

decorator:
  datasource:
    datasource-proxy:
      query:
        enable-logging: false
        log-level: debug # log queries at this level ('logging.level.root' may override this and silence the log)
      slow-query:
        enable-logging: true
        log-level: warn # warn if query runs more than defined threshold of seconds
        threshold: 5s
      multiline: false
      format-sql: false

fundedmind:
  user:
    verification-code:
      expiration: PT30M
      cleanup:
        cron: "@midnight"
        lock-for: "PT86399S" # 24h * 60m * 60s = 86400s
  thread-comment-notification:
    rate-limit-duration: PT15M # 15 minutes

  storage:
    type: GOOGLE_CLOUD_STORAGE
    google-cloud-storage:
      storage-path: https://firebasestorage.googleapis.com
      bucket-name: TODO__SET_IN_PROFILE_PROPERTIES
  security:
    api-key: ${FUNDEDMIND_API_KEY}
    webhook:
      stripe:
        secret-key: ${STRIPE_WEBHOOK_SECRET_KEY}
      hubspot:
        secret-key: ${HUBSPOT_WEBHOOK_SECRET_KEY}
      calendly:
        secret-key: ${CALENDLY_WEBHOOK_SECRET_KEY}
    request-logging:
      enabled: true
  public-data:
    enable-caching: true
    caching:
      refresh-after: 1h
      expire-after: 12h
  url:
    prefix:
      lesson: TODO__SET_IN_PROFILE_PROPERTIES
      networking: TODO__SET_IN_PROFILE_PROPERTIES
  location:
    obfuscation:
      min-radius-meters: 300.0
      max-radius-meters: 500.0

integration:
  email-template:
    base-url: TODO__SET_IN_PROFILE_PROPERTIES
    password: ${EMAIL_TEMPLATE_PASSWORD}
  stripe:
    api-key: ${STRIPE_API_KEY}
    product:
      masterclass: TODO__SET_IN_PROFILE_PROPERTIES
      discord: TODO__SET_IN_PROFILE_PROPERTIES
    discord-billing:
      begin: # empty - override in profiles if needed
    czech-tax-rate: # See 'Tax Rates' section in Product catalog
      inclusive: TODO__SET_IN_PROFILE_PROPERTIES
      exclusive: TODO__SET_IN_PROFILE_PROPERTIES
  hubspot:
    enabled: true
    base-url: https://api.hubapi.com/crm/v3
    api-key: ${HUBSPOT_API_KEY}
  discord:
    enabled: true
    base-url: https://discord.com/api/v10
    client-app-access-token: ${DISCORD_CLIENT_APP_ACCESS_TOKEN}
    guild-id: TODO__SET_IN_PROFILE_PROPERTIES
    student-role-id: TODO__SET_IN_PROFILE_PROPERTIES
    entry-role-id: TODO__SET_IN_PROFILE_PROPERTIES
  calendly:
    enabled: true
    base-url: https://api.calendly.com
    api-key: ${CALENDLY_API_KEY}
    webhook-url: TODO__SET_IN_PROFILE_PROPERTIES
    organization: TODO__SET_IN_PROFILE_PROPERTIES
  fakturoid:
    enabled: true
    base-url: https://app.fakturoid.cz/api/v3
    user-agent: TODO__SET_IN_PROFILE_PROPERTIES
    client-id: ${FAKTUROID_CLIENT_ID}
    client-secret: ${FAKTUROID_CLIENT_SECRET}
    account-name: cleevio
  zapier:
    enabled: true
    base-url: TODO__SET_IN_PROFILE_PROPERTIES
    api-key: ${ZAPIER_API_KEY}
    webhook:
      send-comment-to-slack: TODO__SET_IN_PROFILE_PROPERTIES
      new-game-document: TODO__SET_IN_PROFILE_PROPERTIES
