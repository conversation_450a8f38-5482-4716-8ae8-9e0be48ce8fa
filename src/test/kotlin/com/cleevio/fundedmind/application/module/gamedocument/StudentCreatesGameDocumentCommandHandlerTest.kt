package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.StudentCreatesGameDocumentCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasInsufficientGameLevelException
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentTruthScoreCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class StudentCreatesGameDocumentCommandHandlerTest(
    @Autowired private val underTest: StudentCreatesGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should throw exception when student has insufficient game level`() {
        // Create a student with ZERO game level
        val user = dataHelper.getAppUser(id = 110.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ZERO)
        }

        // Attempt to create a game document with a student that has insufficient game level
        shouldThrow<StudentHasInsufficientGameLevelException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                ),
            )
        }
    }

    @Test
    fun `should upgrade from level ONE to TWO for BACKTESTING document`() {
        // Create a student with level ONE
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.BACKTESTING,
                payoutAmount = BigDecimal.ZERO,
                scoreMessage = "Backtesting completed!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.BACKTESTING
            reachedLevel shouldBe GameLevel.TWO
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @Test
    fun `should keep current level for BACKTESTING document if already higher`() {
        // Create a student with level THREE
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.THREE)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.BACKTESTING,
                payoutAmount = BigDecimal.ZERO,
                scoreMessage = "Backtesting completed!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.BACKTESTING
            reachedLevel shouldBe GameLevel.THREE
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @Test
    fun `should create new game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 103.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.PAYOUT,
                issuingCompany = IssuingCompany.FOR_TRADERS,
                payoutAmount = BigDecimal("1000.00"),
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Great achievement!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.PAYOUT
            issuingCompany shouldBe IssuingCompany.FOR_TRADERS
            payoutAmount shouldBe BigDecimal("1000.00")
            reachedLevel shouldBe GameLevel.TWO
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 95
            scoreMessage shouldBe "Great achievement!"
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }
    }

    @Test
    fun `should create certificate type game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 104.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.CERTIFICATE,
                payoutAmount = BigDecimal.ZERO,
                scoreMessage = "Certificate awarded!",
                truthScore = 100,
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.CERTIFICATE
            issuingCompany shouldBe IssuingCompany.FOR_TRADERS
            payoutAmount shouldBe BigDecimal.ZERO
            reachedLevel shouldBe GameLevel.THREE
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 100
            scoreMessage shouldBe "Certificate awarded!"
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }
    }

    @Test
    fun `should throw exception when payout amount is null for payout document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative payout amount
        shouldThrow<GameDocumentPayoutAmountIsRequiredException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                    payoutAmount = null,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when payout amount is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative payout amount
        shouldThrow<GameDocumentPayoutAmountCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                    payoutAmount = BigDecimal("-100.00"),
                ),
            )
        }
    }

    @Test
    fun `should throw exception when truth score is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative truth score
        shouldThrow<GameDocumentTruthScoreCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    truthScore = -10,
                ),
            )
        }
    }

    private fun defaultCommand(
        studentId: UUID,
        type: GameDocumentType = GameDocumentType.PAYOUT,
        issuingCompany: IssuingCompany = IssuingCompany.FOR_TRADERS,
        payoutAmount: BigDecimal? = BigDecimal("1000.00"),
        payoutDate: LocalDate = LocalDate.now(),
        truthScore: Int = 95,
        scoreMessage: String? = "Great achievement!",
    ) = StudentCreatesGameDocumentCommand(
        studentId = studentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}
