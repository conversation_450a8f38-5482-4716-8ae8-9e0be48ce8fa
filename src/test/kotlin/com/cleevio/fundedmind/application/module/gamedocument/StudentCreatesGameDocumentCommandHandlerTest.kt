package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.command.StudentCreatesGameDocumentCommand
import com.cleevio.fundedmind.application.module.user.student.exception.StudentHasInsufficientGameLevelException
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountCannotBeNegativeException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentPayoutAmountIsRequiredException
import com.cleevio.fundedmind.domain.gamedocument.exception.GameDocumentTruthScoreCannotBeNegativeException
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class StudentCreatesGameDocumentCommandHandlerTest(
    @Autowired private val underTest: StudentCreatesGameDocumentCommandHandler,
    @Autowired private val gameDocumentRepository: GameDocumentRepository,
) : IntegrationTest() {

    @Test
    fun `should throw exception when student has insufficient game level`() {
        // Create a student with ZERO game level
        val user = dataHelper.getAppUser(id = 110.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP, gameLevel = GameLevel.ZERO)
        }

        // Attempt to create a game document with a student that has insufficient game level
        shouldThrow<StudentHasInsufficientGameLevelException> {
            underTest.handle(
                defaultCommand(studentId = user.id),
            )
        }
    }

    @Test
    fun `should upgrade from level ONE to TWO for BACKTESTING document`() {
        // Create a student with level ONE
        val user = dataHelper.getAppUser(id = 111.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = GameLevel.ONE)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.BACKTESTING,
                payoutAmount = null,
                scoreMessage = "Backtesting looks fine!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.BACKTESTING
            reachedLevel shouldBe GameLevel.TWO
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = GameLevel::class,
        names = ["TWO", "THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "NINE", "TEN"],
    )
    fun `should keep current level for BACKTESTING if student is not below level TWO`(currentLevel: GameLevel) {
        // Create a student with level THREE
        val user = dataHelper.getAppUser(id = 112.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = currentLevel)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.BACKTESTING,
                payoutAmount = null,
                scoreMessage = "Backtesting looks fine!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.BACKTESTING
            reachedLevel shouldBe currentLevel
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @ParameterizedTest
    @EnumSource(value = GameLevel::class, names = ["ONE", "TWO"])
    fun `should upgrade to level THREE for CERTIFICATE document`(currentLevel: GameLevel) {
        // Create a student with level ONE
        val user = dataHelper.getAppUser(id = 113.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = currentLevel)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.CERTIFICATE,
                payoutAmount = null,
                scoreMessage = "Certificate looks fine!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.CERTIFICATE
            reachedLevel shouldBe GameLevel.THREE
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = GameLevel::class,
        names = ["THREE", "FOUR", "FIVE", "SIX", "SEVEN", "EIGHT", "NINE", "TEN"],
    )
    fun `should keep current level for CERTIFICATE if student is not below level THREE`(currentLevel: GameLevel) {
        // Create a student with level FOUR
        val user = dataHelper.getAppUser(id = 115.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS, gameLevel = currentLevel)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.CERTIFICATE,
                payoutAmount = BigDecimal.ZERO,
                scoreMessage = "Certificate awarded!",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.CERTIFICATE
            reachedLevel shouldBe currentLevel
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @Test
    fun `should determine level based on total payout amount for PAYOUT document and skip levels FIVE and SIX`() {
        // Create a student with level THREE
        val student = dataHelper.getAppUser(id = 116.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                studentTier = StudentTier.MASTERCLASS,
                gameLevel = GameLevel.FOUR,
            )
        }

        // Create an approved payout document for the student
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = 4_000.toBigDecimal(),
            entityModifier = { it.approve() },
        )

        // Create a new payout document that should push the total to level FIVE (5000)
        val result = underTest.handle(
            defaultCommand(
                studentId = student.id,
                type = GameDocumentType.PAYOUT,
                payoutAmount = 13_000.toBigDecimal(),
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe student.id
            type shouldBe GameDocumentType.PAYOUT
            payoutAmount!! shouldBeEqualComparingTo 13_000.toBigDecimal()
            reachedLevel shouldBe GameLevel.SEVEN
            state shouldBe GameDocumentApprovalState.WAITING
        }
    }

    @Test
    fun `should create new game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 103.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.PAYOUT,
                issuingCompany = IssuingCompany.FOR_TRADERS,
                payoutAmount = 1_000.toBigDecimal(),
                payoutDate = LocalDate.now(),
                truthScore = 95,
                scoreMessage = "Valid payout 95%",
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.PAYOUT
            issuingCompany shouldBe IssuingCompany.FOR_TRADERS
            payoutAmount shouldBe BigDecimal("1000.00")
            reachedLevel shouldBe GameLevel.FOUR
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 95
            scoreMessage shouldBe "Valid payout 95%"
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }
    }

    @Test
    fun `should create certificate type game document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 104.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        val result = underTest.handle(
            defaultCommand(
                studentId = user.id,
                type = GameDocumentType.CERTIFICATE,
                payoutAmount = BigDecimal.ZERO,
                scoreMessage = "Certificate awarded!",
                truthScore = 100,
            ),
        )

        gameDocumentRepository.findByIdOrNull(result.id)!!.run {
            studentId shouldBe user.id
            type shouldBe GameDocumentType.CERTIFICATE
            issuingCompany shouldBe IssuingCompany.FOR_TRADERS
            payoutAmount shouldBe BigDecimal.ZERO
            reachedLevel shouldBe GameLevel.THREE
            payoutDate shouldBe LocalDate.now()
            truthScore shouldBe 100
            scoreMessage shouldBe "Certificate awarded!"
            state shouldBe GameDocumentApprovalState.WAITING
            denyMessage shouldBe null
        }
    }

    @Test
    fun `should throw exception when payout amount is null for payout document`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative payout amount
        shouldThrow<GameDocumentPayoutAmountIsRequiredException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                    payoutAmount = null,
                ),
            )
        }
    }

    @Test
    fun `should throw exception when payout amount is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 107.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative payout amount
        shouldThrow<GameDocumentPayoutAmountCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    type = GameDocumentType.PAYOUT,
                    payoutAmount = BigDecimal("-100.00"),
                ),
            )
        }
    }

    @Test
    fun `should throw exception when truth score is negative`() {
        // Create a student with AppUser
        val user = dataHelper.getAppUser(id = 108.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.MASTERCLASS)
        }

        // Attempt to create with negative truth score
        shouldThrow<GameDocumentTruthScoreCannotBeNegativeException> {
            underTest.handle(
                defaultCommand(
                    studentId = user.id,
                    truthScore = -10,
                ),
            )
        }
    }

    private fun defaultCommand(
        studentId: UUID,
        type: GameDocumentType = GameDocumentType.PAYOUT,
        issuingCompany: IssuingCompany = IssuingCompany.FOR_TRADERS,
        payoutAmount: BigDecimal? = BigDecimal("1000.00"),
        payoutDate: LocalDate = LocalDate.now(),
        truthScore: Int = 95,
        scoreMessage: String? = "Great achievement!",
    ) = StudentCreatesGameDocumentCommand(
        studentId = studentId,
        type = type,
        issuingCompany = issuingCompany,
        payoutAmount = payoutAmount,
        payoutDate = payoutDate,
        truthScore = truthScore,
        scoreMessage = scoreMessage,
    )
}
